{"mission_prior": {"home_position": [52.18293527764432, 91.67762231826784, null], "flight_restricted_area": {"type": "polygon", "coordinates": [[52.18772685877701, 91.67438220977783], [52.187640962146325, 91.68077659606935], [52.17571771723314, 91.68521833419801], [52.17711084329782, 91.67431783676149]]}, "target_areas": [{"id": "area_1", "name": "library", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.178187687906515, 91.68101263046265], [52.17856612739797, 91.68177437782289], [52.17805834996039, 91.68223571777345], [52.17766273150595, 91.68148469924928]]}, "properties": {"height": 15}}, {"id": "area_2", "name": "teaching_building", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.18024313436918, 91.68204259872437], [52.180569959852235, 91.68287944793703], [52.17979537294124, 91.68333005905153], [52.179399887214934, 91.6825683116913]]}, "properties": {"height": 20}}, {"id": "area_3", "name": "laboratory_building", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.181799544748664, 91.6798861026764], [52.18178233279426, 91.68063712120056], [52.181275563282455, 91.68061566352846], [52.181257484665764, 91.6798861026764]]}, "properties": {"height": 18}}, {"id": "area_4", "name": "dormitory_area", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.184655918509605, 91.6771663427353], [52.18466444224811, 91.67742383480073], [52.18447501200689, 91.6774184703827], [52.18447067549101, 91.67715561389925]]}, "properties": {"height": 9}}, {"id": "area_5", "name": "cafeteria", "type": "building", "geometry": {"type": "polygon", "coordinates": [[52.18353694927753, 91.67615246772768], [52.18351974863444, 91.67742919921876], [52.18295181578062, 91.67743992805482], [52.18295181578062, 91.67614173889162]]}, "properties": {"height": 12}}, {"id": "area_6", "name": "track_field", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[52.18701182991439, 91.67929601669313], [52.18700057924048, 91.68035817146303], [52.18537879391133, 91.68037962913515], [52.1853916940822, 91.67927992343904]]}, "properties": {}}, {"id": "area_7", "name": "football_field", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[52.18675374050383, 91.6795427799225], [52.186746786132424, 91.68018651008607], [52.18558958016828, 91.68018651008607], [52.18560097667821, 91.67953205108644]]}, "properties": {}}, {"id": "area_8", "name": "basketball_courts", "type": "sports", "geometry": {"type": "polygon", "coordinates": [[52.18579146905999, 91.67681765556335], [52.185804369161644, 91.67740237712862], [52.185270955633385, 91.67740774154665], [52.18525805544227, 91.67681229114534]]}, "properties": {}}, {"id": "area_9", "name": "yingxue_lake", "type": "water", "geometry": {"type": "polygon", "coordinates": [[52.182737027923125, 91.67796564102174], [52.18272842751261, 91.67910289764406], [52.181420775275065, 91.67902779579164], [52.181425275805104, 91.67798709869386]]}, "properties": {}}, {"id": "area_10", "name": "main_road", "type": "road", "geometry": {"type": "linestring", "coordinates": [[52.18446610232326, 91.67749893665315], [52.18295674948406, 91.67763841152193], [52.18293094832333, 91.67923164367677]]}, "properties": {"width": 8}}, {"id": "area_11", "name": "campus_entrance", "type": "landmark", "geometry": {"type": "polygon", "coordinates": [[52.18380008507551, 91.67593252658844], [52.18415272897228, 91.67593252658844], [52.184152721690644, 91.67609882354736], [52.18380420682054, 91.67609882354736]]}, "properties": {}}]}, "user_requirements": [{"task_id": "1", "descriptions": {"description_en": "Inspect the library roof for any water damage or structural issues.", "professional": "Conduct a high-precision structural inspection of the library roof to generate a detailed damage assessment report, requiring clear identification of micro-cracks and potential leakage points.", "non_professional": "The rainy season is coming, so we must file a detailed report on the roof's condition. You need to fly up there, capture every corner, and I need to be able to zoom in to see even the tiniest cracks."}, "frontal_overlap": {"min": 84.0, "max": 88.0}, "lateral_overlap": {"min": 74.0, "max": 78.0}, "gsd": {"min": 0.8, "max": 1.9}}, {"task_id": "2", "descriptions": {"description_en": "Check the condition of the football field and track to identify any areas needing maintenance.", "professional": "Perform a comprehensive health assessment of the football field turf and track surface, identifying and precisely annotating all areas requiring maintenance for the annual detailed maintenance plan.", "non_professional": "It's time to report the year-end budget. Can you help me survey the entire field in detail? Mark out any bald patches on the grass or damage on the track, otherwise, I can't ask management for money to fix it next year."}, "frontal_overlap": {"min": 73.0, "max": 77.0}, "lateral_overlap": {"min": 63.0, "max": 67.0}, "gsd": {"min": 2.4, "max": 3.6}}, {"task_id": "3", "descriptions": {"description_en": "Survey the dormitory area to assess external building conditions and nearby facilities.", "professional": "Conduct a rapid survey of the dormitory area to assess the general condition of building facades and surrounding public facilities, generating an overview report.", "non_professional": "I need this for a meeting this afternoon. Can you quickly fly over to the dorms and just snap a few photos? Just a rough look to see if there are any major issues with the buildings and roads is fine. No need for detail, just give me a general idea."}, "frontal_overlap": {"min": 76.0, "max": 80.0}, "lateral_overlap": {"min": 66.0, "max": 70.0}, "gsd": {"min": 2.0, "max": 3.2}}, {"task_id": "4", "descriptions": {"description_en": "Capture high-resolution photos of Yingxue Lake to monitor water levels and surrounding vegetation.", "professional": "Acquire multi-angle, high-resolution imagery of Yingxue Lake for precise analysis of the current water line, water eutrophication levels, and shoreline vegetation coverage and health. The data will be used for academic research.", "non_professional": "The data for this project is critical; we need to analyze the relationship between the lake water and the surrounding vegetation. Fly over there and capture the water color, water level, and the condition of the plants on the shore crystal clear. Not a single detail can be missed."}, "frontal_overlap": {"min": 72.0, "max": 76.0}, "lateral_overlap": {"min": 62.0, "max": 66.0}, "gsd": {"min": 2.5, "max": 3.7}}, {"task_id": "5", "descriptions": {"description_en": "Create a detailed map of the basketball courts.", "professional": "Perform an orthomosaic survey of the basketball courts to generate an ultra-high-precision map with a GSD no worse than 1.6 cm/pixel, intended for the fine analysis of ground line wear.", "non_professional": "We need to issue a formal report on the basketball court's maintenance, and we need the absolute clearest photo as a base map. It must be sharp enough to see every single scratch and the tiniest crack on the ground. There's zero room for ambiguity."}, "frontal_overlap": {"min": 76.0, "max": 80.0}, "lateral_overlap": {"min": 66.0, "max": 70.0}, "gsd": {"min": 1.9, "max": 3.1}}, {"task_id": "7", "descriptions": {"description_en": "Perform a comprehensive scan of the teaching building exterior.", "professional": "Execute a high-precision 3D reconstruction scan of the teaching building's facade. Ensure complete data integrity for archival purposes.", "non_professional": "We need to create a digital model of this teaching building for our archives, so every facade and every corner must be captured. Nothing can be missed, to ensure the final model is both complete and accurate."}, "frontal_overlap": {"min": 85.0, "max": 89.0}, "lateral_overlap": {"min": 75.0, "max": 79.0}, "gsd": {"min": 0.8, "max": 1.8}}, {"task_id": "8", "descriptions": {"description_en": "Fly along the main road and capture photos to document campus traffic flow.", "professional": "Execute a rapid fly-through along the main road to roughly document peak-hour traffic flow. The objective is to capture the general traffic distribution without need for fine-detail recognition.", "non_professional": "I just want to see how congested the main road gets during the evening rush hour. Just do a quick pass from one end to the other. I just need a general idea, no need to take careful shots."}, "frontal_overlap": {"min": 62.0, "max": 66.0}, "lateral_overlap": {"min": 52.0, "max": 56.0}, "gsd": {"min": 4.0, "max": 5.2}}, {"task_id": "9", "descriptions": {"description_en": "Photograph the laboratory building from multiple angles to document its current state.", "professional": "Capture a set of high-quality imagery of the laboratory building via multi-angle, multi-altitude orbital flights to comprehensively document the building's current external condition for submission to the engineering department.", "non_professional": "The engineering department needs a status report on the lab building. Please help me get shots of all sides of the building, make them clear, and ensure the information is complete so they can't say our submission is inadequate."}, "frontal_overlap": {"min": 83.0, "max": 87.0}, "lateral_overlap": {"min": 73.0, "max": 77.0}, "gsd": {"min": 1.0, "max": 2.2}}, {"task_id": "10", "descriptions": {"description_en": "Inspect the cafeteria roof and surrounding area for potential maintenance issues.", "professional": "Conduct a standard inspection of the cafeteria roof and its surrounding ancillary facilities (e.g., exhaust ducts, AC units) to identify all visible potential maintenance risks and generate an inspection checklist.", "non_professional": "It's safety inspection month again. Can you check the cafeteria roof and all those pipes and equipment next to it? Look for anything loose or rusty so I can fill out the report form."}, "frontal_overlap": {"min": 81.0, "max": 85.0}, "lateral_overlap": {"min": 71.0, "max": 75.0}, "gsd": {"min": 1.2, "max": 2.4}}, {"task_id": "11", "descriptions": {"description_en": "Create an orthomosaic map of the entire campus.", "professional": "Execute a campus-wide aerial survey mission to generate a complete, seamlessly stitched, high-precision orthomosaic map, to be used for future digital campus planning.", "non_professional": "This is one of the most important projects for our university, to create a brand new, definitive digital map. You must cover the entire campus, and the final stitched map cannot have any misalignments or blurring. All future planning will rely on this map."}, "frontal_overlap": {"min": 86.0, "max": 90.0}, "lateral_overlap": {"min": 76.0, "max": 80.0}, "gsd": {"min": 0.8, "max": 1.7}}, {"task_id": "12", "descriptions": {"description_en": "Capture aerial photos of the campus entrance and main roads for campus navigation materials.", "professional": "To produce campus navigation materials, please capture aerial imagery of the main campus entrance and key roads. The imagery should be well-composed and clear, explicitly showing road directions and landmark buildings.", "non_professional": "We want to create a nice and practical navigation guide for new students. Can you help me get some shots of the main gate and the main roads? They need to be clear enough for people to understand the layout at a glance."}, "frontal_overlap": {"min": 72.0, "max": 76.0}, "lateral_overlap": {"min": 62.0, "max": 66.0}, "gsd": {"min": 2.5, "max": 3.7}}]}